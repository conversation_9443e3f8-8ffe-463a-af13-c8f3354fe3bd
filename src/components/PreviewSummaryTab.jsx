import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Download, 
  Mail, 
  MessageSquare, 
  MoreHorizontal,
  Edit,
  Upload,
  Plus,
  ChevronDown,
  Signature,
  FileText,
  Settings,
  CreditCard,
  Eye,
  History,
  BarChart3
} from 'lucide-react';
import { formatCurrency } from '../utils/formatCurrency';
import { templates } from '../utils/templateRegistry';

const PreviewSummaryTab = ({ 
  invoiceData,
  businessData,
  clientData,
  items,
  taxPercentage,
  selectedCurrency,
  notes,
  setNotes,
  termsConditions,
  setTermsConditions,
  signature,
  setSignature,
  pixData,
  setPixData,
  onTemplateSelect
}) => {
  const calculateSubTotal = () => {
    return items.reduce((sum, item) => sum + (item.amount || 0), 0);
  };

  const calculateTaxAmount = () => {
    const subTotal = calculateSubTotal();
    return (subTotal * taxPercentage) / 100;
  };

  const calculateGrandTotal = () => {
    return calculateSubTotal() + calculateTaxAmount();
  };

  const handleTermChange = (index, value) => {
    const newTerms = [...termsConditions];
    newTerms[index] = { ...newTerms[index], text: value };
    setTermsConditions(newTerms);
  };

  const addNewTerm = () => {
    setTermsConditions([...termsConditions, { text: "" }]);
  };

  const removeTerm = (index) => {
    if (termsConditions.length > 1) {
      setTermsConditions(termsConditions.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="space-y-8">
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Invoice Summary</h2>
          <p className="text-gray-600 mt-1">Review your invoice and customize the final details</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="w-4 h-4 mr-2" />
            Record Payment
          </Button>
          <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>
          <div className="flex">
            <Button variant="outline" size="sm" className="rounded-r-none">
              <Mail className="w-4 h-4 mr-2" />
              Email
            </Button>
            <Button variant="outline" size="sm" className="rounded-l-none border-l-0">
              <MessageSquare className="w-4 h-4 mr-2" />
              WhatsApp
            </Button>
          </div>
          <Button variant="outline" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Invoice Preview */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center justify-between">
                <span>Invoice Preview</span>
                <Button variant="ghost" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  Full Preview
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Invoice Header */}
              <div className="bg-purple-50 p-6 rounded-lg mb-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-2xl font-bold text-purple-900 mb-2">Invoice</h3>
                    <div className="space-y-1 text-sm">
                      <p><span className="font-medium">Invoice No:</span> #{invoiceData.number}</p>
                      <p><span className="font-medium">Invoice Date:</span> {invoiceData.date}</p>
                      <p><span className="font-medium">Due Date:</span> {invoiceData.paymentDate}</p>
                    </div>
                  </div>
                  <div className="w-20 h-20 bg-purple-200 rounded-lg flex items-center justify-center">
                    {invoiceData.logoUrl ? (
                      <img src={invoiceData.logoUrl} alt="Logo" className="w-full h-full object-cover rounded-lg" />
                    ) : (
                      <div className="text-purple-600 text-xs text-center">Your Logo</div>
                    )}
                  </div>
                </div>
              </div>

              {/* From/To Section */}
              <div className="grid grid-cols-2 gap-6 mb-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-600 mb-2">From</h4>
                  <div className="text-sm space-y-1">
                    <p className="font-medium">{businessData.name || 'Fluxo Services'}</p>
                    <p>{businessData.address || 'R. Cel Tamarindo, Franca, São Paulo, Brazil'}</p>
                    <p>VAT Number: {businessData.vatNumber || '18'}</p>
                    <p>Phone: {businessData.phone || '+55 16 84598 9898'}</p>
                  </div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-600 mb-2">For</h4>
                  <div className="text-sm space-y-1">
                    <p className="font-medium">{clientData.name || 'Contabilizei Contabilidade'}</p>
                    <p>{clientData.address || 'São Paulo, Brazil'}</p>
                  </div>
                </div>
              </div>

              {/* Items Table */}
              <div className="border rounded-lg overflow-hidden mb-6">
                <table className="w-full text-sm">
                  <thead className="bg-purple-600 text-white">
                    <tr>
                      <th className="px-4 py-2 text-left">Item</th>
                      <th className="px-4 py-2 text-center">VAT</th>
                      <th className="px-4 py-2 text-center">Quantity</th>
                      <th className="px-4 py-2 text-right">Rate</th>
                      <th className="px-4 py-2 text-right">Amount</th>
                      <th className="px-4 py-2 text-right">VAT</th>
                      <th className="px-4 py-2 text-right">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {items.map((item, index) => (
                      <tr key={index} className="border-b">
                        <td className="px-4 py-3">
                          <div>
                            <p className="font-medium">{item.name || 'IT Service'}</p>
                            {item.description && (
                              <p className="text-xs text-gray-600">{item.description}</p>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center">{item.tax || taxPercentage}%</td>
                        <td className="px-4 py-3 text-center">{item.quantity}</td>
                        <td className="px-4 py-3 text-right">{formatCurrency(item.rate, selectedCurrency)}</td>
                        <td className="px-4 py-3 text-right">{formatCurrency(item.amount, selectedCurrency)}</td>
                        <td className="px-4 py-3 text-right">{formatCurrency((item.amount * (item.tax || taxPercentage)) / 100, selectedCurrency)}</td>
                        <td className="px-4 py-3 text-right font-medium">{formatCurrency(item.amount + ((item.amount * (item.tax || taxPercentage)) / 100), selectedCurrency)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Totals */}
              <div className="flex justify-end mb-6">
                <div className="w-64 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Amount</span>
                    <span>{formatCurrency(calculateSubTotal(), selectedCurrency)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>VAT</span>
                    <span>{formatCurrency(calculateTaxAmount(), selectedCurrency)}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold">
                      <span>Total ({selectedCurrency})</span>
                      <span>{formatCurrency(calculateGrandTotal(), selectedCurrency)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Template Selection */}
              <div>
                <h4 className="font-semibold mb-4">Choose Template</h4>
                <div className="grid grid-cols-3 gap-4">
                  {templates.slice(0, 6).map((template, index) => (
                    <div
                      key={index}
                      className="border-2 border-gray-200 rounded-lg p-3 cursor-pointer hover:border-purple-500 transition-colors"
                      onClick={() => onTemplateSelect(index + 1)}
                    >
                      <div className="aspect-[3/4] bg-gray-100 rounded mb-2 flex items-center justify-center">
                        <span className="text-xs text-gray-500">Template {index + 1}</span>
                      </div>
                      <p className="text-xs text-center font-medium">{template.name}</p>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Invoice Summary Collapsible */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-base">
                <div className="flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Invoice Summary
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </CardTitle>
            </CardHeader>
          </Card>

          {/* Signature */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base">
                <Signature className="w-4 h-4 mr-2" />
                Signature
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                {signature ? (
                  <img src={signature} alt="Signature" className="max-w-full h-auto" />
                ) : (
                  <div>
                    <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                    <p className="text-sm text-gray-500">Upload signature</p>
                  </div>
                )}
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Signature
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  Use Signature Pad
                </Button>
              </div>
              <div>
                <Label htmlFor="signatureLabel" className="text-sm font-medium">Add signature label</Label>
                <Input
                  id="signatureLabel"
                  placeholder="Authorised Signatory"
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base">
                <Plus className="w-4 h-4 mr-2" />
                Add Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Notas adicionais: Lorem ipsum dolor sit amet"
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Terms and Conditions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base">
                <FileText className="w-4 h-4 mr-2" />
                Add Terms & Conditions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {termsConditions.map((term, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <span className="text-sm font-medium mt-2">{String(index + 1).padStart(2, '0')}.</span>
                    <Textarea
                      value={term.text}
                      onChange={(e) => handleTermChange(index, e.target.value)}
                      placeholder="Enter term or condition"
                      rows={2}
                      className="flex-1"
                    />
                    {termsConditions.length > 1 && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeTerm(index)}
                        className="text-red-500 mt-1"
                      >
                        <Plus className="w-4 h-4 rotate-45" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={addNewTerm}
                  className="text-blue-600"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Term
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-blue-600"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Group
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Additional Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base">
                <Plus className="w-4 h-4 mr-2" />
                Add Additional Info
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Input
                  placeholder="PIX"
                  value="<EMAIL>"
                  readOnly
                  className="bg-gray-50"
                />
                <Button variant="ghost" size="sm" className="text-blue-600">
                  <Plus className="w-4 h-4 mr-2" />
                  Add More Fields
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Advanced Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-base">
                <span>Your Business Profile is Ready</span>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </CardTitle>
            </CardHeader>
          </Card>

          {/* Additional Cards */}
          <div className="space-y-2">
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Settings className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Customize Invoice Design</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <CreditCard className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Bank Details</span>
                  <span className="text-xs bg-gray-200 px-2 py-1 rounded ml-2">Not enabled</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Settings className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Online Payment Options</span>
                  <span className="text-xs bg-gray-200 px-2 py-1 rounded ml-2">Not enabled</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">View Journal</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <History className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Acceptance History</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <BarChart3 className="w-4 h-4 mr-2 text-gray-500" />
                  <span className="text-sm font-medium">Audit Trail</span>
                </div>
                <Button variant="ghost" size="icon">
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewSummaryTab;